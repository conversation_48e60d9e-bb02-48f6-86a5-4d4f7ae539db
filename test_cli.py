#!/usr/bin/env python3
"""
CLI program to test the task_based_generation_agent functionality.
<PERSON><PERSON> interrupts by prompting user for input and continuing execution.
"""

import json
import sys

from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.errors import Command
from langgraph.checkpoint.memory import MemorySaver

from graph.task_based_generation_agent import create_task_based_generation_graph
from agent_prompt.task_based_generation_agent import TASK_BASED_GENERATION_AGENT_PROMPT
from models.state import State
from models.workflowGraph import WorkflowGraph
import json


def print_banner():
    """Print a welcome banner."""
    print("=" * 60)
    print("  Task-Based Generation Agent CLI")
    print("=" * 60)
    print()


def get_example_task() -> str:
    """Return an example task for workflow generation."""
    return """
task_key: 1
title: Write a short mystery story
details: Write a short mystery story (around 200-300 words) with the following elements: a locked room mystery, an unexpected detective (perhaps an unlikely character), and a surprising twist ending. Make it engaging and suspenseful.
plan: Use AgenticAI tool to generate the story
workflow: {}
"""


def run_graph(task: str):
    """Run the task-based generation graph with the given task, handling interrupts."""
    print("\n" + "=" * 60)
    print(f"Task: {task}")
    print("=" * 60)

    # Create checkpointer for state persistence
    checkpointer = MemorySaver()

    # Create the graph
    graph = create_task_based_generation_graph(checkpointer=checkpointer)

    # Initial state
    config = {"configurable": {"thread_id": "task-gen-thread-1"}}
    initial_workflow = WorkflowGraph(nodes={}, edges={})
    initial_workflow.add_node(
        node_id="start-node",
        label="Start",
        OriginalType="StartNode",
        type="component",
        position=(0, 0),
        parameters={},
    )

    # Create initial message with the task
    initial_message = HumanMessage(content=task.format(json.dumps(initial_workflow.get_graph_repr())))

    initial_state = State(
        task_based_generation_messages=[
            SystemMessage(content=TASK_BASED_GENERATION_AGENT_PROMPT),
            initial_message,
        ],
        workflow_graph=initial_workflow,
        nodes_to_test=[],
        mcps_infos={},
        verified_mcp_data={},
        dummy_data="",
        unauthorized_mcps=[],
        output="",
        main_agent_messages=[],
        planner_messages=[],
        workflow_generation_messages=[],
    )

    print("\nStarting graph execution...")
    print("-" * 60)

    # Track if this is the first run or a continuation
    output = graph.invoke(initial_state, config=config)
    while True:
        try:
            print("\nOUTPUT:")
            print("=" * 80)
            print(output)
            print("=" * 80)
            if "__interrupt__" not in output:
                break
            # Handle the interrupt

            print("\n" + "!" * 60)
            print("INTERRUPT - Graph is asking for input:")
            print("!" * 60)
            interrupt_value = output["__interrupt__"]
            print(f"\n{interrupt_value[0].value}\n")

            # Get user input
            print("Enter your response (or 'quit' to exit):")
            user_input = input("> ").strip()

            if user_input.lower() == "quit":
                print("\nExiting...")
                break

            # Update the state with user's response and continue
            print(f"\nContinuing with input: {user_input}")
            output = graph.invoke(Command(resume=user_input), config)

        except Exception as e:
            print(f"\n[ERROR] Graph execution failed: {e}")
            import traceback

            traceback.print_exc()
            break

    # Print final workflow graph if available
    if "workflow_graph" in output:
        print("\n" + "=" * 60)
        print("FINAL WORKFLOW GRAPH:")
        print("=" * 60)
        print(json.dumps(output["workflow_graph"].get_graph_repr(), indent=2))


def main():
    """Main CLI function."""
    print_banner()

    print("Enter a task for workflow generation:")
    print("\nExample:")
    print(f'  "{get_example_task()}"')
    print("\nOr press Enter to use the example above:")

    user_input = input().strip()

    if user_input == "":
        task = get_example_task()
        print("\nUsing example task")
    else:
        task = user_input

    run_graph(task)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nInterrupted by user. Goodbye!")
        sys.exit(0)
