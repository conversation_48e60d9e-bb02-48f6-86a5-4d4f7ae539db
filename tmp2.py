from models.workflowGraph import WorkflowGraph

func = {
    "name": "add_node",
    "args": {
        "OriginalType": "MCP_Google_Gemini_google_gemini_generate_content_from_text",
        "label": "Generate Mystery Story",
        "mcp_id": "d3ee35ff-a37c-42d6-be56-1894f3199812",
        "node_id": "story-generator",
        "parameters": {
            "model": "gemini-pro",
            "responseFormat": False,
            "text": "Write a short mystery story (around 200-300 words) with the following elements: a locked room mystery, an unexpected detective (perhaps an unlikely character), and a surprising twist ending. Make it engaging and suspenseful.",
        },
        "position": [200, 0],
        "tool_name": "google_gemini-generate-content-from-text",
        "type": "mcp",
    },
    "id": "toolu_01U9b7ScFEbh1zqcb3rbacmn",
    "type": "tool_call",
}

workflow = WorkflowGraph()

workflow.add_node(
    node_id=func["args"]["node_id"],
    label=func["args"]["label"],
    OriginalType=func["args"]["OriginalType"],
    type=func["args"]["type"],
    position=func["args"]["position"],
    parameters=func["args"]["parameters"],
    mcp_id=func["args"]["mcp_id"],
    tool_name=func["args"]["tool_name"],
)