# example of list of nodes to be tested
# [
#     {
#         "type":"mcp",
#         "mcp_id":"6801c41e0850440001103c30",
#         "tool_name":"send_email"
#         "name_slug":"sendgrid"
#     },
#     {
#         "type":"component",
#         "OriginalType":"StartNode",
#     },
#     {
#         "type":"workflow",
#         "workflow_id":"8685aab6-8dab-4dd5-ac80-21d513224911",
#     }
# ]

import asyncio
import os

from langchain_core.messages import HumanMessage, SystemMessage, ToolMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph
from langgraph.types import Command, interrupt

from models.state import State
from utils.context import get_context
from utils.tool_schema import EXECUTER_MCP_TOOL_SCHEMA
from utils.workflows import is_mcp_authorized, mcp_call

http_bearer = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KtjAcdDwSLpO6O3J34uThZtKmeWpt43R--9uVW888VQ"

REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")
USER_ID = "cb762802-322d-43ac-9507-15dd8b2dd1e1"


def get_llm():
    """Initialize and return LLM with tool binding."""
    try:
        llm = ChatOpenAI(
            base_url="https://router.requesty.ai/v1",
            model="anthropic/claude-sonnet-4",
            api_key=REQUESTY_API_KEY,
        )
        llm = llm.bind_tools([EXECUTER_MCP_TOOL_SCHEMA])
        return llm
    except Exception as e:
        raise RuntimeError(f"Failed to initialize LLM: {str(e)}")


def check_authorization(state: State):
    """
    Step 1: Check authorization for MCP nodes.
    Routes to authorization node if needed, otherwise continues to get_mcp_info.
    If there are no MCP nodes, skips directly to get_mcp_info (which will then skip to END).
    """
    print("\n" + "=" * 80)
    print("NODE: check_authorization")
    print("=" * 80)

    print("\n[STEP 1] Checking authorization for MCP nodes...")

    # Check if there are any MCP nodes
    mcp_nodes = [node for node in state.nodes_to_test if node.get("type") == "mcp"]

    if not mcp_nodes:
        print(f"[STEP 1] No MCP nodes found, skipping to get_mcp_info")
        return Command(goto="get_mcp_info")

    unauthorized_mcps = []
    for node in mcp_nodes:
        try:
            name_slug = node.get("name_slug")
            if not name_slug:
                raise ValueError(f"Missing 'name_slug' in node: {node}")

            if not is_mcp_authorized(name_slug, httpbearer=http_bearer):
                unauthorized_mcps.append(node)
        except Exception as e:
            print(f"Error checking authorization for node {node}: {str(e)}")
            unauthorized_mcps.append(node)

    # If there are unauthorized MCPs, go to authorization node
    if len(unauthorized_mcps) > 0:
        print(f"[STEP 1] Found {len(unauthorized_mcps)} unauthorized MCPs")
        print(f"[STEP 1] Routing to ask_user_to_authorize node")
        return Command(
            update={"unauthorized_mcps": unauthorized_mcps},
            goto="ask_user_to_authorize",
        )

    print(f"[STEP 1] All MCPs authorized ✓")
    return Command(goto="get_mcp_info")


def get_mcp_info(state: State):
    """
    Step 2: Get information for all nodes.
    Retrieves context for each MCP tool, component, and workflow.
    For components and workflows, also generates output directly.
    """
    print("\n" + "=" * 80)
    print("NODE: get_mcp_info")
    print("=" * 80)

    print("\n[STEP 2] Getting node information...")
    mcps_infos = {}
    output = state.output  # Preserve existing output

    # Separate nodes by type
    mcp_nodes = []
    non_mcp_nodes = []

    for node in state.nodes_to_test:
        if node.get("type") == "mcp":
            mcp_nodes.append(node)
        else:
            non_mcp_nodes.append(node)

    # Process MCP nodes
    for node in mcp_nodes:
        try:
            mcp_id = node.get("mcp_id")
            tool_name = node.get("tool_name")

            if not mcp_id or not tool_name:
                raise ValueError(f"Missing 'mcp_id' or 'tool_name' in node: {node}")

            context = get_context("mcp", type_id=mcp_id, tool_name=tool_name)
            mcps_infos[f"{mcp_id}-{tool_name}"] = context
            print(f"[STEP 2] Retrieved context for MCP {mcp_id}-{tool_name}")
        except Exception as e:
            error_msg = f"Error getting context for MCP {node.get('mcp_id')}: {str(e)}"
            print(error_msg)
            mcps_infos[f"{node.get('mcp_id')}-{node.get('tool_name')}"] = error_msg

    # Process component and workflow nodes - get context and generate output directly
    for node in non_mcp_nodes:
        if node.get("type") == "component":
            try:
                OriginalType = node.get("OriginalType")
                context = get_context("component", OriginalType)
                mcps_infos[OriginalType] = context
                print(f"[STEP 2] Retrieved context for component {OriginalType}")

                # Generate output directly for component
                output += f"""
Component: {OriginalType}
Context: {context}

"""
            except Exception as e:
                error_msg = (
                    f"Error getting context for component {OriginalType}: {str(e)}"
                )
                print(error_msg)
                mcps_infos[OriginalType] = error_msg
                output += f"\n{error_msg}\n"
        elif node.get("type") == "workflow":
            try:
                workflow_id = node.get("workflow_id")
                context = get_context("workflow", type_id=workflow_id)
                mcps_infos[workflow_id] = context
                print(f"[STEP 2] Retrieved context for workflow {workflow_id}")

                # Generate output directly for workflow
                output += f"""
Workflow: {workflow_id}
Context: {context}

"""
            except Exception as e:
                error_msg = (
                    f"Error getting context for workflow {workflow_id}: {str(e)}"
                )
                print(error_msg)
                mcps_infos[workflow_id] = error_msg
                output += f"\n{error_msg}\n"

    print(f"[STEP 2] Retrieved {len(mcps_infos)} node contexts ✓")

    # If there are MCP nodes, continue to ask_for_dummy_data
    # If only non-MCP nodes, skip to END
    if mcp_nodes:
        print(
            f"[STEP 2] Found {len(mcp_nodes)} MCP nodes, continuing to ask_for_dummy_data"
        )
        return Command(update={"mcps_infos": mcps_infos, "output": output}, goto="ask_for_dummy_data")
    else:
        print(f"[STEP 2] No MCP nodes found, skipping to END")
        tool_call_id = state.task_based_generation_messages[-1].tool_calls[0]["id"]
        return {
            "task_based_generation_messages": [
                ToolMessage(content=output, tool_call_id=tool_call_id)
            ]
        }


def ask_for_dummy_data(state: State):
    """
    Step 3: Ask LLM to generate a question for dummy data for MCP tools only, then interrupt to get user input.
    This node handles the human-in-the-loop interaction.
    """
    print("\n" + "=" * 80)
    print("NODE: ask_for_dummy_data")
    print("=" * 80)

    print("\n[STEP 3] Asking LLM for dummy data for MCP tools...")

    # Filter only MCP tools from mcps_infos
    mcp_infos_only = {
        key: value
        for key, value in state.mcps_infos.items()
        if any(
            node.get("type") == "mcp"
            and f"{node.get('mcp_id')}-{node.get('tool_name')}" == key
            for node in state.nodes_to_test
        )
    }

    try:
        llm = ChatOpenAI(
            base_url="https://router.requesty.ai/v1",
            model="anthropic/claude-sonnet-4",
            api_key=REQUESTY_API_KEY,
        )
        response = llm.invoke(
            [
                SystemMessage(
                    content="You are a helpful assistant. Ask the user for dummy data for the following MCP tools."
                ),
                HumanMessage(content=f"MCP tools: {mcp_infos_only}"),
            ]
        )
        print(f"[STEP 3] LLM response received ✓")
    except Exception as e:
        error_msg = f"Error asking for dummy data: {str(e)}"
        print(error_msg)
        response = type(
            "obj",
            (object,),
            {"content": "Please provide dummy data for the MCP tools."},
        )()

    print("\n[INTERRUPT] Waiting for user to provide dummy data...")
    print(f"[INTERRUPT] LLM Question: {response.content}")
    print("-" * 80)
    dummy_data = interrupt(f"{response.content}")
    print("-" * 80)
    print(f"[INTERRUPT] User response received: {dummy_data}")
    print(f"[STEP 3] Dummy data collected ✓")

    return {"dummy_data": dummy_data}


def execute_mcp_tools(state: State):
    """
    Step 4: Execute MCP tools with the collected dummy data.
    Uses LLM to generate tool calls and executes them.
    Only processes MCP tools - components and workflows are already handled in get_mcp_info.
    """
    print("\n" + "=" * 80)
    print("NODE: execute_mcp_tools")
    print("=" * 80)

    print("\n[STEP 4] Executing MCP tools...")
    output = state.output  # Preserve existing output from components/workflows

    # Filter only MCP nodes
    mcp_nodes = [node for node in state.nodes_to_test if node.get("type") == "mcp"]

    # Filter only MCP tools from mcps_infos
    mcp_infos_only = {
        key: value
        for key, value in state.mcps_infos.items()
        if any(
            node.get("type") == "mcp"
            and f"{node.get('mcp_id')}-{node.get('tool_name')}" == key
            for node in state.nodes_to_test
        )
    }

    try:
        llm = get_llm()
        response = llm.invoke(
            [
                SystemMessage(
                    content="You are a helpful assistant. Execute the following MCP tool."
                ),
                HumanMessage(
                    content=f"MCP tools: {mcp_nodes}\nMCP tools Context: {mcp_infos_only}\nDummy data: {state.dummy_data}"
                ),
            ]
        )
        print(f"[STEP 4] LLM generated {len(response.tool_calls)} tool calls")

        # Execute each tool call
        for node_call in response.tool_calls:
            try:
                mcp_id = node_call["args"]["mcp_id"]
                name_slug = node_call["args"]["name_slug"]
                tool_name = node_call["args"]["tool_name"]
                input_data = node_call["args"]["input_data"]
                # Find the corresponding node
                node = next(
                    (
                        node
                        for node in mcp_nodes
                        if node.get("mcp_id") == mcp_id
                        and node.get("tool_name") == tool_name
                    ),
                    None,
                )

                if not node:
                    print(f"[STEP 4] Node with id {mcp_id}-{tool_name} not found")
                    output += f"\nError: Node with id {mcp_id}-{tool_name} not found\n"
                    continue

                if not all([name_slug, tool_name, input_data, USER_ID]):
                    print(
                        f"[STEP 4] Missing required fields in node {mcp_id}-{tool_name}"
                    )
                    output += f"\nError: Missing required fields in node {node}\n"
                    continue

                # Execute the MCP call
                print(f"[STEP 4] Executing MCP call: {name_slug}/{tool_name}")
                result = asyncio.run(
                    mcp_call(name_slug, tool_name, input_data, USER_ID)
                )
                print(f"[STEP 4] MCP call completed ✓")

                output += f"""
MCP: {mcp_id}-{tool_name}
Context: {state.mcps_infos.get(f"{mcp_id}-{tool_name}", "N/A")}
Dummy data: {state.dummy_data}
Result: {result}

"""
            except Exception as e:
                error_msg = f"Error executing tool call {node_call}: {str(e)}"
                print(
                    f"[STEP 4] Error executing tool call {mcp_id}-{tool_name}: {str(e)}"
                )
                print(error_msg)
                output += f"\n{error_msg}\n"

    except Exception as e:
        error_msg = f"Error in tool execution phase: {str(e)}"
        print(error_msg)
        output += f"\n{error_msg}\n"

    print(f"[STEP 4] All MCP tools executed ✓")
    tool_call_id = state.task_based_generation_messages[-1].tool_calls[0]["id"]
    return {
        "task_based_generation_messages": [
            ToolMessage(content=output, tool_call_id=tool_call_id)
        ]
    }


def ask_user_to_authorize(state: State):
    """
    Authorization node that interrupts to ask user to authorize MCPs.
    After authorization, returns to main process.
    """
    print("\n" + "=" * 80)
    print("ENTERING: ask_user_to_authorize")
    print("=" * 80)

    if not state.unauthorized_mcps:
        # No unauthorized MCPs, go back to main process
        print("[AUTH] No unauthorized MCPs, returning to check_authorization")
        print("=" * 80 + "\n")
        return Command(goto="check_authorization")

    # Format the unauthorized MCPs for display
    print(f"[AUTH] Found {len(state.unauthorized_mcps)} unauthorized MCPs")
    mcp_list = []
    for mcp in state.unauthorized_mcps:
        mcp_info = f"- {mcp.get('name_slug', 'Unknown')} (Tool: {mcp.get('tool_name', 'Unknown')})"
        mcp_list.append(mcp_info)

    message = f"Please authorize the following MCPs:\n" + "\n".join(mcp_list)

    print("\n[INTERRUPT] Waiting for user to authorize MCPs...")
    print(f"[INTERRUPT] Authorization request: {message}")
    print("-" * 80)
    interrupt(message)
    print("-" * 80)
    print("[INTERRUPT] User authorization received ✓")
    print("[AUTH] Clearing unauthorized MCPs and returning to check_authorization")
    print("=" * 80 + "\n")

    # After user authorizes, clear unauthorized list and return to main process
    return Command(update={"unauthorized_mcps": []}, goto="check_authorization")


def create_test_node_graph(checkpointer=None):
    """
    Create a multi-node graph with clear separation of concerns:
    1. check_authorization - validates MCP authorization (MCP only)
    2. ask_user_to_authorize - handles authorization interrupts (MCP only)
    3. get_mcp_info - retrieves context for all node types:
       - For MCP tools: retrieves context and continues to ask_for_dummy_data
       - For components/workflows: retrieves context, generates output, and skips to END
    4. ask_for_dummy_data - interrupts to get user input for dummy data (MCP only)
    5. execute_mcp_tools - executes the MCP tools with collected data (MCP only)

    Flow:
    - MCP tools: check_authorization -> get_mcp_info -> ask_for_dummy_data -> execute_mcp_tools -> END
    - Components/Workflows: check_authorization -> get_mcp_info -> END (context only, no execution)
    """
    test_node_graph = StateGraph(State)

    # Add all nodes
    test_node_graph.add_node("check_authorization", check_authorization)
    test_node_graph.add_node("ask_user_to_authorize", ask_user_to_authorize)
    test_node_graph.add_node("get_mcp_info", get_mcp_info)
    test_node_graph.add_node("ask_for_dummy_data", ask_for_dummy_data)
    test_node_graph.add_node("execute_mcp_tools", execute_mcp_tools)

    # Set entry point
    test_node_graph.set_entry_point("check_authorization")

    # Define the flow:
    # check_authorization -> get_mcp_info (if authorized, via Command it can also go to ask_user_to_authorize)
    # ask_user_to_authorize -> check_authorization (retry after authorization)
    # get_mcp_info -> ask_for_dummy_data (for MCP tools) OR END (for components/workflows via Command)
    # ask_for_dummy_data -> execute_mcp_tools
    # execute_mcp_tools -> END

    test_node_graph.add_edge("ask_for_dummy_data", "execute_mcp_tools")
    test_node_graph.add_edge("execute_mcp_tools", END)
    test_node_graph.add_edge("ask_user_to_authorize", "check_authorization")

    return test_node_graph.compile(checkpointer=checkpointer)
