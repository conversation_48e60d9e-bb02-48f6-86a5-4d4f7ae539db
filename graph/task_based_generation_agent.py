import json
import os
from copy import deepcopy

import requests
from langchain_core.messages import ToolMessage
from langchain_openai import Chat<PERSON>penAI
from langgraph.graph import END, StateGraph
from langgraph.types import Overwrite

from models.state import State
from utils.context import get_context
from utils.RAG import RAG_search
from utils.tool_schema import (
    ADD_EDGE_SCHEMA,
    ADD_NODE_SCHEMA,
    COMPILE_SCHEMA,
    DELETE_EDGE_SCHEMA,
    DELETE_NODE_SCHEMA,
    GET_CONTEXT_SCHEMA,
    GET_GRAPH_REPR_SCHEMA,
    GET_NODE_INFO_SCHEMA,
    RAG_TOOL_SCHEMA,
    UPDATE_NODE_SCHEMA,
)
from utils.workflows import execute_workflow, get_workflow, is_authorized, save_workflow


def get_llm():
    REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")
    llm = ChatOpenAI(
        client_args={
            "api_key": REQUESTY_API_KEY,
            "base_url": "https://router.requesty.ai/v1",
        },
        model_id="anthropic/claude-sonnet-4",
    )
    llm = llm.bind_tools(
        [
            RAG_TOOL_SCHEMA,
            GET_CONTEXT_SCHEMA,
            ADD_NODE_SCHEMA,
            ADD_EDGE_SCHEMA,
            DELETE_NODE_SCHEMA,
            DELETE_EDGE_SCHEMA,
            UPDATE_NODE_SCHEMA,
            GET_GRAPH_REPR_SCHEMA,
            GET_NODE_INFO_SCHEMA,
            COMPILE_SCHEMA,
        ]
    )
    return llm


def agent_node(state: State):
    update = {"task_based_generation_messages": []}
    llm = get_llm()
    response = llm.invoke(state.task_based_generation_messages)
    update["task_based_generation_messages"].append(response)
    if not response.tool_calls:
        update["workflow_graph"] = deepcopy(state.task_workflow_graph)
    return update


def tools_node(state: State):
    update = {
        "task_based_generation_messages": [],
    }
    tool_calls = state.task_based_generation_messages[-1].tool_calls
    for tool_call in tool_calls:
        print(tool_call)
        if tool_call["name"] == "RAG_search":
            try:
                result = RAG_search(
                    tool_call["args"]["query"], tool_call["args"].get("k", 10)
                )
                result = ToolMessage(
                    content=json.dumps(result),
                    tool_call_id=tool_call["id"],
                    id=tool_call["id"],
                )
                update["task_based_generation_messages"].append(result)
                print(f"RAG_search : {result.content}")
                print()
            except Exception as e:
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content=f"Error searching RAG: {str(e)}",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"Error searching RAG: {str(e)}")
                print()
        if tool_call["name"] == "get_context":
            try:
                result = get_context(**tool_call["args"])
                result = ToolMessage(
                    content=result, tool_call_id=tool_call["id"], id=tool_call["id"]
                )
                update["task_based_generation_messages"].append(result)
                print(f"get_context : {result.content}")
                print()
            except Exception as e:
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content=f"Error getting context: {str(e)}",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"Error getting context: {str(e)}")
                print()
        if tool_call["name"] == "add_node":
            try:
                state.task_workflow_graph.add_node(
                    tool_call["args"]["node_id"],
                    tool_call["args"]["label"],
                    tool_call["args"]["OriginalType"],
                    tool_call["args"]["type"],
                    tool_call["args"]["position"],
                    tool_call["args"]["parameters"],
                    tool_call["args"].get("mcp_id"),
                    tool_call["args"].get("workflow_id"),
                    tool_call["args"].get("tool_name"),
                )
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content="Node added successfully.",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"add_node : {tool_call['args']}")
                print()
            except Exception as e:
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content=f"Error adding node: {str(e)}",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"Error adding node: {str(e)}")
                print()
        if tool_call["name"] == "add_edge":
            try:
                state.task_workflow_graph.add_edge(
                    tool_call["args"]["source"],
                    tool_call["args"]["sourceHandle"],
                    tool_call["args"]["target"],
                    tool_call["args"]["targetHandle"],
                )
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content="Edge added successfully.",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"add_edge : {tool_call['args']}")
                print()
            except Exception as e:
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content=f"Error adding edge: {str(e)}",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"Error adding edge: {str(e)}")
                print()
        if tool_call["name"] == "delete_node":
            try:
                state.task_workflow_graph.delete_node(tool_call["args"]["node_id"])
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content="Node deleted successfully.",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"delete_node : {tool_call['args']}")
                print()
            except Exception as e:
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content=f"Error deleting node: {str(e)}",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"Error deleting node: {str(e)}")
                print()
        if tool_call["name"] == "delete_edge":
            try:
                state.task_workflow_graph.delete_edge(
                    tool_call["args"]["source"],
                    tool_call["args"]["sourceHandle"],
                    tool_call["args"]["target"],
                    tool_call["args"]["targetHandle"],
                )
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content="Edge deleted successfully.",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"delete_edge : {tool_call['args']}")
                print()
            except Exception as e:
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content=f"Error deleting edge: {str(e)}",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"Error deleting edge: {str(e)}")
                print()
        if tool_call["name"] == "update_node":
            try:
                state.task_workflow_graph.update_node(
                    tool_call["args"]["node_id"], tool_call["args"]["parameters"]
                )
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content="Node updated successfully.",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"update_node : {tool_call['args']}")
                print()
            except Exception as e:
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content=f"Error updating node: {str(e)}",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"Error updating node: {str(e)}")
                print()
        if tool_call["name"] == "get_graph_repr":
            try:
                result = state.task_workflow_graph.get_graph_repr()
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content=json.dumps(result),
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"get_graph_repr : {result}")
                print()
            except Exception as e:
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content=f"Error getting graph representation: {str(e)}",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"Error getting graph representation: {str(e)}")
                print()
        if tool_call["name"] == "get_node_info":
            try:
                result = state.task_workflow_graph.get_node_info(
                    tool_call["args"]["node_id"]
                )
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content=json.dumps(result),
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"get_node_info : {result}")
                print()
            except Exception as e:
                update["task_based_generation_messages"].append(
                    ToolMessage(
                        content=f"Error getting node information: {str(e)}",
                        tool_call_id=tool_call["id"],
                        id=tool_call["id"],
                    )
                )
                print(f"Error getting node information: {str(e)}")
                print()
    update["task_workflow_graph"] = state.task_workflow_graph
    return update


def router(state: State):
    tool_calls = state.task_based_generation_messages[-1].tool_calls
    if tool_calls:
        return "tools"
    else:
        return "end"


def create_task_based_generation_graph():
    task_based_generation_graph = StateGraph(State)
    task_based_generation_graph.add_node("agent", agent_node)
    task_based_generation_graph.add_node("tools", tools_node)
    task_based_generation_graph.add_conditional_edges(
        "agent", router, {"tools": "tools", "end": END}
    )
    task_based_generation_graph.add_edge("tools", "agent")
    task_based_generation_graph.set_entry_point("agent")
    return task_based_generation_graph.compile()
