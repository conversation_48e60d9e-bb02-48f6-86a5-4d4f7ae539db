"""
Things to change
1. make every tool a node
2. improve the creating of todo list
3. add a initial question for preference.
"""

import json
import os

from langchain_core.messages import ToolMessage, HumanMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph
from langgraph.types import interrupt

from models.state import State
from utils.RAG import RAG_search
from utils.todo import add_todo, delete_todo, get_todos
from utils.tool_schema import (
    ADD_TODO_SCHEMA,
    DELETE_TODO_SCHEMA,
    GET_CURRENT_WORKFLOW_SCHEMA,
    GET_TODOS_SCHEMA,
    RAG_TOOL_SCHEMA,
)


def get_llm():
    REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")
    llm = ChatOpenAI(
        base_url="https://router.requesty.ai/v1",
        model="anthropic/claude-sonnet-4",
        api_key=REQUESTY_API_KEY,
    )
    llm = llm.bind_tools(
        [
            RAG_TOOL_SCHEMA,
            GET_CURRENT_WORKFLOW_SCHEMA,
            GET_TODOS_SCHEMA,
            ADD_TODO_SCHEMA,
            DELETE_TODO_SCHEMA,
        ]
    )
    return llm

def ask_user_preference(state: State):
    print("\n=== ASK USER PREFERENCE ===")
    print("Asking user for preference")
    preference = interrupt("Do you have Any preference for the mcp you want to use, If not then just type no preference, I will decide for you.")
    update = {"planner_messages": [
        HumanMessage(content=f"""
        User preference: {preference}
        requirement: {state.requirement}
        previous_plan: {state.plan}
        workflow: {json.dumps(state.workflow_graph.get_graph_repr())}
""")
    ]}
    return update

def agent_node(state: State) -> State:
    print("\n=== AGENT NODE ===")
    print(f"Processing {len(state.planner_messages)} messages")

    update = {}
    llm = get_llm()
    response = llm.invoke(state.planner_messages)
    update["planner_messages"] = [response]

    if not response.tool_calls:
        print("No tool calls - setting plan and moving to main stage")
        update["plan"] = response.content
    else:
        print(f"Agent requested {len(response.tool_calls)} tool call(s):")
        for tool_call in response.tool_calls:
            print(f"  - {tool_call['name']}")

    return update


# Individual tool nodes
def rag_search_node(state: State) -> State:
    print("\n=== RAG SEARCH NODE ===")
    update = {"planner_messages": []}
    tool_calls = state.planner_messages[-1].tool_calls

    for tool_call in tool_calls:
        if tool_call["name"] == "RAG_search":
            query = tool_call["args"]["query"]
            k = tool_call["args"].get("k", 10)
            print(f"Searching with query: '{query}' (k={k})")

            result = RAG_search(query)
            update["planner_messages"].append(
                ToolMessage(content=json.dumps(result), tool_call_id=tool_call["id"])
            )
            print(f"Found {len(result) if isinstance(result, list) else 'N/A'} results")

    return update


def get_current_workflow_node(state: State) -> State:
    print("\n=== GET CURRENT WORKFLOW NODE ===")
    update = {"planner_messages": []}
    tool_calls = state.planner_messages[-1].tool_calls

    for tool_call in tool_calls:
        if tool_call["name"] == "get_current_workflow":
            print("Retrieving current workflow graph representation")

            workflow_repr = state.workflow_graph.get_graph_repr()
            update["planner_messages"].append(
                ToolMessage(
                    content=json.dumps(workflow_repr),
                    tool_call_id=tool_call["id"],
                )
            )
            print(
                f"Workflow has {len(workflow_repr.get('nodes', []))} nodes and {len(workflow_repr.get('edges', []))} edges"
            )

    return update


def get_todos_node(state: State) -> State:
    print("\n=== GET TODOS NODE ===")
    update = {"planner_messages": []}
    tool_calls = state.planner_messages[-1].tool_calls

    for tool_call in tool_calls:
        if tool_call["name"] == "get_todos":
            print("Retrieving todo list")

            todos = get_todos(state)
            update["planner_messages"].append(
                ToolMessage(content=json.dumps(todos), tool_call_id=tool_call["id"])
            )
            print(f"Found {len(todos)} todo(s)")

    return update


def add_todo_node(state: State) -> State:
    print("\n=== ADD TODO NODE ===")
    update = {"planner_messages": [], "todo": []}
    tool_calls = state.planner_messages[-1].tool_calls

    for tool_call in tool_calls:
        if tool_call["name"] == "add_todo":
            task_key = tool_call["args"]["task_key"]
            title = tool_call["args"]["title"]
            details = tool_call["args"]["details"]
            plan = tool_call["args"]["plan"]

            print(f"Adding todo: task_key='{task_key}', title='{title}'")

            todo = add_todo(task_key, title, details, plan)
            update["todo"].append(todo)
            update["planner_messages"].append(
                ToolMessage(
                    content="Todo added successfully.", tool_call_id=tool_call["id"]
                )
            )
            print("Todo added successfully")

    return update


def delete_todo_node(state: State) -> State:
    print("\n=== DELETE TODO NODE ===")
    update = {"planner_messages": [], "todo": []}
    tool_calls = state.planner_messages[-1].tool_calls

    for tool_call in tool_calls:
        if tool_call["name"] == "delete_todo":
            todo_key = tool_call["args"]["todo"]
            print(f"Deleting todo: '{todo_key}'")

            todo = delete_todo(todo_key)
            update["todo"].append(todo)
            update["planner_messages"].append(
                ToolMessage(
                    content="Todo deleted successfully.", tool_call_id=tool_call["id"]
                )
            )
            print("Todo deleted successfully")

    return update


def router(state: State):
    print("\n=== ROUTER ===")
    tool_calls = state.planner_messages[-1].tool_calls

    if not tool_calls:
        print("No tool calls - routing to END")
        return "end"

    # Route to the appropriate tool node based on the first tool call
    tool_name = tool_calls[0]["name"]
    print(f"Routing to tool: {tool_name}")

    tool_routing = {
        "RAG_search": "rag_search",
        "get_current_workflow": "get_current_workflow",
        "get_todos": "get_todos",
        "add_todo": "add_todo",
        "delete_todo": "delete_todo",
    }

    return tool_routing.get(tool_name, "end")


def create_planner_graph(checkpointer=None):
    print("\n=== CREATING PLANNER GRAPH ===")
    planner_graph = StateGraph(State)

    # Add nodes
    planner_graph.add_node("agent", agent_node)
    planner_graph.add_node("rag_search", rag_search_node)
    planner_graph.add_node("get_current_workflow", get_current_workflow_node)
    planner_graph.add_node("get_todos", get_todos_node)
    planner_graph.add_node("add_todo", add_todo_node)
    planner_graph.add_node("delete_todo", delete_todo_node)

    # Add conditional edges from agent to tools
    planner_graph.add_conditional_edges(
        "agent",
        router,
        {
            "rag_search": "rag_search",
            "get_current_workflow": "get_current_workflow",
            "get_todos": "get_todos",
            "add_todo": "add_todo",
            "delete_todo": "delete_todo",
            "end": END,
        },
    )

    # Add edges from tools back to agent
    planner_graph.add_edge("rag_search", "agent")
    planner_graph.add_edge("get_current_workflow", "agent")
    planner_graph.add_edge("get_todos", "agent")
    planner_graph.add_edge("add_todo", "agent")
    planner_graph.add_edge("delete_todo", "agent")

    planner_graph.set_entry_point("agent")

    print("Planner graph created successfully")
    return planner_graph.compile(checkpointer=checkpointer)
