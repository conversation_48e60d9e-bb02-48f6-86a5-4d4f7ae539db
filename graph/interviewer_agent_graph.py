import json
import os
from typing import Any, Dict

from langchain_core.messages import HumanMessage, SystemMessage, ToolMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph

from agent_prompt.interviewer_agent import INTERVIEWER_AGENT_PROMPT
from models.state import State
from utils.tool_schema import (
    ANALYZE_REQUIREMENTS_SCHEMA,
    ASK_CLARIFICATION_SCHEMA,
    FINALIZE_REQUIREMENTS_SCHEMA,
)


def get_llm():
    return ChatOpenAI(
        model="gpt-4o-mini",
        temperature=0,
        api_key=os.getenv("OPENAI_API_KEY"),
    ).bind_tools(
        [
            ANALYZE_REQUIREMENTS_SCHEMA,
            ASK_CLARIFICATION_SCHEMA,
            FINALIZE_REQUIREMENTS_SCHEMA,
        ]
    )


def agent_node(state: State):
    """Main interviewer agent node that processes user input and decides next action."""
    print("\n=== INTERVIEWER AGENT NODE ===")

    update = {}
    llm = get_llm()

    # Initialize interviewer messages if empty
    if not state.interviewer_messages:
        system_message = SystemMessage(content=INTERVIEWER_AGENT_PROMPT)

        # Create initial context message
        context = f"""
        User's initial prompt: {state.user_prompt or "No initial prompt provided"}
        Current requirements: {json.dumps(state.workflow_requirements or {}, indent=2)}
        Missing information: {state.missing_information}
        Previous questions asked: {state.clarification_questions}
        Interview complete: {state.interview_complete}
        """

        human_message = HumanMessage(content=context)
        update["interviewer_messages"] = [system_message, human_message]

    response = llm.invoke(state.interviewer_messages)
    update["interviewer_messages"] = [response]

    print(f"Agent response: {response.content}")
    if response.tool_calls:
        print(f"Tool calls: {[tc['name'] for tc in response.tool_calls]}")

    return update


def analyze_requirements_node(state: State):
    """Analyze current requirements and identify gaps."""
    print("\n=== ANALYZE REQUIREMENTS NODE ===")

    tool_call = state.interviewer_messages[-1].tool_calls[0]

    try:
        # Extract current requirements from state and user responses
        current_requirements = state.workflow_requirements or {}
        user_prompt = state.user_prompt or ""

        # Analyze what information we have and what's missing
        essential_categories = [
            "workflow_purpose",
            "input_data",
            "processing_logic",
            "output_requirements",
            "integration_needs",
            "constraints",
            "error_handling",
        ]

        missing_info = []
        for category in essential_categories:
            if (
                category not in current_requirements
                or not current_requirements[category]
            ):
                missing_info.append(category)

        analysis_result = {
            "current_requirements": current_requirements,
            "missing_categories": missing_info,
            "completeness_score": (len(essential_categories) - len(missing_info))
            / len(essential_categories),
            "next_questions_needed": len(missing_info) > 0,
        }

        result_message = ToolMessage(
            content=json.dumps(analysis_result, indent=2),
            tool_call_id=tool_call["id"],
        )

        # Update state with analysis results
        update = {
            "interviewer_messages": [result_message],
            "missing_information": missing_info,
        }

        print(f"Analysis complete. Missing: {missing_info}")

    except Exception as e:
        error_msg = f"Error analyzing requirements: {str(e)}"
        result_message = ToolMessage(
            content=error_msg,
            tool_call_id=tool_call["id"],
        )
        update = {"interviewer_messages": [result_message]}
        print(f"Analysis error: {error_msg}")

    return update


def ask_clarification_node(state: State):
    """Ask specific clarification questions to gather missing information."""
    print("\n=== ASK CLARIFICATION NODE ===")

    tool_call = state.interviewer_messages[-1].tool_calls[0]

    try:
        questions = tool_call["args"].get("questions", [])
        category = tool_call["args"].get("category", "general")

        # Store the questions being asked
        new_questions = state.clarification_questions + questions

        # Format questions for user
        question_text = f"\nTo better understand your workflow requirements for {category}, I need to ask:\n\n"
        for i, question in enumerate(questions, 1):
            question_text += f"{i}. {question}\n"

        result_message = ToolMessage(
            content=question_text,
            tool_call_id=tool_call["id"],
        )

        update = {
            "interviewer_messages": [result_message],
            "clarification_questions": new_questions,
        }

        print(f"Asked {len(questions)} clarification questions for {category}")

    except Exception as e:
        error_msg = f"Error asking clarification: {str(e)}"
        result_message = ToolMessage(
            content=error_msg,
            tool_call_id=tool_call["id"],
        )
        update = {"interviewer_messages": [result_message]}
        print(f"Clarification error: {error_msg}")

    return update


def finalize_requirements_node(state: State):
    """Finalize the requirements gathering and prepare for planning phase."""
    print("\n=== FINALIZE REQUIREMENTS NODE ===")

    tool_call = state.interviewer_messages[-1].tool_calls[0]

    try:
        final_requirements = tool_call["args"].get("requirements", {})
        summary = tool_call["args"].get("summary", "")

        # Create comprehensive requirements document
        requirements_doc = {
            "workflow_purpose": final_requirements.get("workflow_purpose", ""),
            "input_data": final_requirements.get("input_data", {}),
            "processing_logic": final_requirements.get("processing_logic", []),
            "output_requirements": final_requirements.get("output_requirements", {}),
            "integration_needs": final_requirements.get("integration_needs", []),
            "constraints": final_requirements.get("constraints", []),
            "error_handling": final_requirements.get("error_handling", {}),
            "additional_notes": final_requirements.get("additional_notes", ""),
            "summary": summary,
        }

        result_message = ToolMessage(
            content=f"Requirements gathering complete!\n\nSummary: {summary}\n\nDetailed requirements have been documented and are ready for the planning phase.",
            tool_call_id=tool_call["id"],
        )

        update = {
            "interviewer_messages": [result_message],
            "workflow_requirements": requirements_doc,
            "interview_complete": True,
            "requirement": summary,  # Set this for compatibility with existing system
        }

        print("Requirements finalized successfully")

    except Exception as e:
        error_msg = f"Error finalizing requirements: {str(e)}"
        result_message = ToolMessage(
            content=error_msg,
            tool_call_id=tool_call["id"],
        )
        update = {"interviewer_messages": [result_message]}
        print(f"Finalization error: {error_msg}")

    return update


def router(state: State):
    """Route to appropriate tool based on agent's decision."""
    print("\n=== INTERVIEWER ROUTER ===")

    tool_calls = state.interviewer_messages[-1].tool_calls

    if tool_calls:
        tool_name = tool_calls[0]["name"]
        print(f"Routing to: {tool_name}")
        return tool_name
    else:
        print("No tool calls - ending interview")
        return "end"


def create_interviewer_graph(checkpointer=None):
    """Create the interviewer agent graph."""
    print("\n=== CREATING INTERVIEWER GRAPH ===")

    interviewer_graph = StateGraph(State)

    # Add nodes
    interviewer_graph.add_node("agent", agent_node)
    interviewer_graph.add_node("analyze_requirements", analyze_requirements_node)
    interviewer_graph.add_node("ask_clarification", ask_clarification_node)
    interviewer_graph.add_node("finalize_requirements", finalize_requirements_node)

    # Add conditional edges from agent to tools
    interviewer_graph.add_conditional_edges(
        "agent",
        router,
        {
            "analyze_requirements": "analyze_requirements",
            "ask_clarification": "ask_clarification",
            "finalize_requirements": "finalize_requirements",
            "end": END,
        },
    )

    # Add edges from tools back to agent
    interviewer_graph.add_edge("analyze_requirements", "agent")
    interviewer_graph.add_edge("ask_clarification", "agent")
    interviewer_graph.add_edge("finalize_requirements", "agent")

    # Set entry point
    interviewer_graph.set_entry_point("agent")

    print("Interviewer graph created successfully")
    return interviewer_graph.compile(checkpointer=checkpointer)
