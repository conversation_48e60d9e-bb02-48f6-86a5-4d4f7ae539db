"""Requirements and benchmark gathering logic.

This module handles the conversational extraction of requirements
and benchmark specifications from user input using an LLM.

Includes:
- Single-shot extraction (extract_requirements)
- Conversational gathering (RequirementsGatherer class)
"""

import logging
from dataclasses import dataclass, field
from typing import Any

from .llm import call_llm, call_llm_for_json
from .state import Benchmark<PERSON><PERSON><PERSON>, BenchmarkSpec

REQUIREMENTS_SYSTEM_PROMPT = """You are an expert AI agent architect. Your job is to help users define requirements for a new LangGraph agent.

Ask clarifying questions to understand:
1. What problem the agent should solve
2. What inputs it will receive
3. What outputs it should produce
4. What external tools or APIs it needs access to
5. What success looks like (for benchmarking)

Be conversational but focused. Extract concrete, actionable requirements.
When you have enough information, summarize the requirements in a structured format.

Output format when ready:
```json
{
  "agent_name": "suggested_name_snake_case",
  "description": "One sentence description",
  "inputs": ["list of input types"],
  "outputs": ["list of output types"],
  "tools": ["list of tools needed"],
  "constraints": ["any constraints or requirements"],
  "ready": true
}
```

If you need more information, set "ready": false and include a "question" field.
"""

logger = logging.getLogger(__name__)

# =============================================================================
# Conversational Requirements Gathering
# =============================================================================

GATHERER_SYSTEM_PROMPT = """You are a requirements analyst for an AI agent builder.
Your job is to have a conversation with the user to understand what agent they want to build.

You must gather:
1. **Purpose**: What is the main goal of the agent?
2. **Inputs**: What data/information will the agent receive?
3. **Outputs**: What should the agent produce/return?
4. **Steps**: What are the key steps/operations the agent should perform?
5. **Constraints**: Any limitations, error handling, or special cases?

Ask ONE clarifying question at a time. Be concise and friendly.
When you have enough information to build the agent, say "REQUIREMENTS_COMPLETE" followed by a summary.

Current conversation:
{conversation_history}
"""

EXTRACT_STRUCTURED_PROMPT = """Based on this conversation, extract a structured requirements list.

Conversation:
{conversation_history}

Output a JSON object:
{{
    "agent_name": "snake_case_name",
    "purpose": "Brief description of what the agent does",
    "requirements": [
        {{"id": "REQ-001", "description": "Requirement", "category": "input|output|processing|error"}}
    ],
    "inputs": [{{"name": "input_name", "type": "str", "description": "what it is"}}],
    "outputs": [{{"name": "output_name", "type": "str", "description": "what it is"}}]
}}
"""


@dataclass
class GatheredRequirements:
    """Structured requirements gathered from conversation."""

    agent_name: str
    purpose: str
    requirements: list[dict]
    inputs: list[dict]
    outputs: list[dict]
    raw_conversation: list[dict] = field(default_factory=list)


class RequirementsGatherer:
    """Conversational requirements gathering via LLM."""

    def __init__(self) -> None:
        self.conversation_history: list[dict] = []
        self.is_complete = False

    def get_initial_prompt(self) -> str:
        """Get the opening message to start the conversation."""
        return (
            "Hi! I'll help you design an AI agent.\n"
            "What kind of agent would you like to build? Describe what it should do."
        )

    def process_user_input(self, user_message: str) -> str:
        """Process user input and return the next question or completion.

        Args:
            user_message: What the user said

        Returns:
            LLM's response (question or completion message)
        """
        self.conversation_history.append({"role": "user", "content": user_message})
        history_str = self._format_conversation()

        response = call_llm(
            system_prompt=GATHERER_SYSTEM_PROMPT.format(
                conversation_history=history_str,
            ),
            user_message=user_message,
        )

        if "REQUIREMENTS_COMPLETE" in response:
            self.is_complete = True

        self.conversation_history.append({"role": "assistant", "content": response})
        return response

    def extract_structured_requirements(self) -> GatheredRequirements:
        """Extract structured requirements from the conversation."""
        history_str = self._format_conversation()

        result = call_llm_for_json(
            system_prompt="Extract structured requirements from this conversation.",
            user_message=EXTRACT_STRUCTURED_PROMPT.format(
                conversation_history=history_str
            ),
        )

        return GatheredRequirements(
            agent_name=result.get("agent_name", "my_agent"),
            purpose=result.get("purpose", ""),
            requirements=result.get("requirements", []),
            inputs=result.get("inputs", []),
            outputs=result.get("outputs", []),
            raw_conversation=self.conversation_history.copy(),
        )

    def _format_conversation(self) -> str:
        """Format conversation history as string."""
        lines = []
        for msg in self.conversation_history:
            role = "User" if msg["role"] == "user" else "Assistant"
            lines.append(f"{role}: {msg['content']}")
        return "\n".join(lines) if lines else "(No conversation yet)"


# =============================================================================
# Single-shot Requirements Extraction (Original)
# =============================================================================


def extract_requirements(user_input: str) -> dict[str, Any]:
    """Extract requirements from user input using LLM.

    Args:
        user_input: Natural language description of the desired agent

    Returns:
        Dict with extracted requirements:
        - agent_name: Suggested snake_case name
        - description: One-sentence description
        - inputs: List of input types
        - outputs: List of output types
        - tools: List of needed tools
        - constraints: Any constraints
        - ready: Whether requirements are complete
        - question: Follow-up question if not ready
    """
    logger.info("Extracting requirements from user input")

    try:
        result = call_llm_for_json(
            system_prompt=REQUIREMENTS_SYSTEM_PROMPT,
            user_message=user_input,
        )
        logger.debug(f"Extracted requirements: {result}")
        return result
    except ValueError as e:
        logger.warning(f"Failed to extract requirements: {e}")
        # Return a default structure asking for clarification
        return {
            "ready": False,
            "question": "I couldn't fully understand your requirements. Could you please describe what kind of agent you want to build? What problem should it solve?",
        }


def extract_benchmarks(
    requirements: dict[str, Any], user_input: str | None = None
) -> dict[str, Any]:
    """Extract benchmark examples based on requirements.

    Args:
        requirements: Previously extracted requirements dict
        user_input: Optional additional user input about benchmarks

    Returns:
        Dict with benchmark specification:
        - examples: List of benchmark examples
        - pass_threshold: Score threshold for passing
        - ready: Whether benchmarks are complete
    """
    logger.info("Extracting benchmark examples")

    # Build context message
    context = f"""Requirements for the agent:
- Name: {requirements.get('agent_name', 'unknown')}
- Description: {requirements.get('description', 'No description')}
- Inputs: {requirements.get('inputs', [])}
- Outputs: {requirements.get('outputs', [])}
- Tools: {requirements.get('tools', [])}
"""
    if user_input:
        context += f"\nUser's additional input about benchmarks:\n{user_input}"
    else:
        context += "\nPlease suggest appropriate benchmark examples based on these requirements."

    try:
        result = call_llm_for_json(
            system_prompt=BENCHMARK_GATHERING_PROMPT,
            user_message=context,
        )
        logger.debug(f"Extracted benchmarks: {result}")
        return result
    except ValueError as e:
        logger.warning(f"Failed to extract benchmarks: {e}")
        return {
            "examples": [],
            "pass_threshold": 0.8,
            "ready": False,
        }


def build_benchmark_spec(benchmark_data: dict[str, Any]) -> BenchmarkSpec:
    """Convert raw benchmark data to BenchmarkSpec model.

    Args:
        benchmark_data: Dict from extract_benchmarks()

    Returns:
        BenchmarkSpec model
    """
    examples = []
    for ex in benchmark_data.get("examples", []):
        try:
            examples.append(
                BenchmarkExample(
                    name=ex.get("name", "unnamed"),
                    user_messages=ex.get("user_messages", []),
                    expected_behavior=ex.get("expected_behavior", {}),
                    evaluator=ex.get("evaluator", "contains"),
                )
            )
        except Exception as e:
            logger.warning(f"Failed to parse benchmark example: {e}")
            continue

    return BenchmarkSpec(
        examples=examples,
        pass_threshold=benchmark_data.get("pass_threshold", 0.8),
        scoring=benchmark_data.get("scoring", "per_example_avg"),
    )


def build_requirements_summary(requirements: dict[str, Any]) -> str:
    """Build a human-readable requirements summary.

    Args:
        requirements: Extracted requirements dict

    Returns:
        Formatted string summary
    """
    lines = [
        f"Agent: {requirements.get('agent_name', 'unknown')}",
        f"Description: {requirements.get('description', 'No description')}",
        f"Inputs: {', '.join(requirements.get('inputs', []))}",
        f"Outputs: {', '.join(requirements.get('outputs', []))}",
        f"Tools: {', '.join(requirements.get('tools', []))}",
    ]
    if requirements.get("constraints"):
        lines.append(f"Constraints: {', '.join(requirements['constraints'])}")
    return "\n".join(lines)

def gather_requirements() -> GatheredRequirements:
    """Run the conversational requirements gathering loop."""
    print_section("Phase 1: Requirements Gathering")

    gatherer = RequirementsGatherer()

    # Print initial prompt
    print(f"Agent: {gatherer.get_initial_prompt()}\n")

    # Conversation loop
    while not gatherer.is_complete:
        try:
            user_input = input("You: ").strip()
            if not user_input:
                continue

            if user_input.lower() in ("quit", "exit", "q"):
                print("\nExiting...")
                sys.exit(0)

            response = gatherer.process_user_input(user_input)
            print(f"\nAgent: {response}\n")

        except KeyboardInterrupt:
            print("\n\nExiting...")
            sys.exit(0)

    # Extract structured requirements
    print_section("Extracting Structured Requirements")
    requirements = gatherer.extract_structured_requirements()

    print(f"Agent Name: {requirements.agent_name}")
    print(f"Purpose: {requirements.purpose}")
    print("\nRequirements:")
    for req in requirements.requirements:
        print(f"  [{req.get('id', '?')}] {req.get('description', '')}")
    print(f"\nInputs: {[i.get('name') for i in requirements.inputs]}")
    print(f"Outputs: {[o.get('name') for o in requirements.outputs]}")

    return requirements

