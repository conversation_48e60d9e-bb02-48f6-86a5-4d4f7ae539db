def get_start_node_data(workflow):
    output = []
    nodes = workflow["nodes"]
    edges = workflow["edges"]
    start_node_edges = [edge for edge in edges if edge["source"] == "start-node"]
    for edge in start_node_edges:
        target_node = next(
            (node for node in nodes if node["id"] == edge["target"]), None
        )
        target_input = next(
            (
                input
                for input in target_node["data"]["definition"]["inputs"]
                if input["name"] == edge["targetHandle"]
            ),
            None,
        )
        output.append(
            {
                "description": target_input["info"],
                "field": target_input["name"],
                "type": target_input["input_type"],
                "required": target_input["required"],
            }
        )
    return output
