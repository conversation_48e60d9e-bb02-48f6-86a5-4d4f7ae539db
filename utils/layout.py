import math
from typing import Dict, List, Optional, Tu<PERSON>

import numpy as np

# Constants for better performance and readability
EPSILON = 1e-6
DEFAULT_RANDOM_DISPLACEMENT = 1.0


def calculate_repulsive_force(
    rect1: Dict, rect2: Dict, separation: float, strength: float
) -> Tuple[float, float]:
    """
    Calculate repulsive force between two rectangles.

    Args:
        rect1, rect2: Rectangle dictionaries with 'x', 'y', 'w', 'h'
        separation: Minimum separation distance
        strength: Force strength multiplier

    Returns:
        Tuple of (force_x, force_y) applied to rect1
    """
    # Calculate centers (optimized with single calculation)
    center1_x = rect1["x"] + rect1["w"] * 0.5
    center1_y = rect1["y"] + rect1["h"] * 0.5
    center2_x = rect2["x"] + rect2["w"] * 0.5
    center2_y = rect2["y"] + rect2["h"] * 0.5

    # Calculate distance between centers
    dx = center1_x - center2_x
    dy = center1_y - center2_y
    distance_squared = dx * dx + dy * dy
    distance = math.sqrt(distance_squared)

    if distance < EPSILON:  # Very small distance, avoid division by zero
        # Apply small random displacement
        dx = np.random.uniform(-1, 1)
        dy = np.random.uniform(-1, 1)
        distance = DEFAULT_RANDOM_DISPLACEMENT

    # Calculate minimum distance needed based on rectangle dimensions
    min_distance_x = (rect1["w"] + rect2["w"]) * 0.5 + separation
    min_distance_y = (rect1["h"] + rect2["h"]) * 0.5 + separation

    # Use the larger of the two minimum distances to ensure proper separation
    min_distance = max(min_distance_x, min_distance_y)

    # Only apply force if rectangles are too close
    if distance < min_distance:
        # Calculate force magnitude - use a more controlled approach
        overlap = min_distance - distance
        force_magnitude = strength * overlap

        # Normalize direction vector (avoid division by zero already handled above)
        inv_distance = 1.0 / distance
        dx_norm = dx * inv_distance
        dy_norm = dy * inv_distance

        # Calculate force components
        force_x = force_magnitude * dx_norm
        force_y = force_magnitude * dy_norm

        return force_x, force_y

    return 0.0, 0.0


def rectangle_layout_with_separation(
    rectangles: List[Dict],
    fixed_node_id: Optional[str] = None,
    separation: float = 20.0,
    max_iterations: int = 500,
    convergence_threshold: float = 0.5,
    force_strength: float = 0.2,
    damping: float = 0.85,
    step_size: float = 2.0,
) -> List[Dict]:
    """
    Arrange rectangles with no overlap and fixed separation using force-directed layout.

    Args:
        rectangles: List of dictionaries with keys 'id', 'x', 'y', 'w', 'h'
        fixed_node_id: ID of the node to keep fixed in position (optional)
        separation: Minimum separation distance between rectangles (default: 20.0)
        max_iterations: Maximum number of iterations for the algorithm (default: 500)
        convergence_threshold: Threshold for convergence (default: 0.5)
        force_strength: Strength of repulsive forces (default: 0.2)
        damping: Damping factor to reduce oscillations (default: 0.85)
        step_size: Maximum step size per iteration (default: 2.0)

    Returns:
        List of rectangles with updated positions
    """
    if not rectangles:
        return rectangles

    # Create a copy to avoid modifying the original
    nodes = [rect.copy() for rect in rectangles]
    num_nodes = len(nodes)

    # Find the fixed node index (optimized lookup)
    fixed_index = None
    if fixed_node_id:
        node_id_to_index = {node["id"]: i for i, node in enumerate(nodes)}
        fixed_index = node_id_to_index.get(fixed_node_id)

    # Initialize velocities as numpy arrays for better performance
    velocities = np.zeros((num_nodes, 2), dtype=np.float64)

    # Pre-calculate step_size_squared for optimization
    step_size_squared = step_size * step_size

    for _ in range(max_iterations):
        # Initialize forces array
        forces = np.zeros((num_nodes, 2), dtype=np.float64)
        max_movement = 0.0

        # Calculate repulsive forces between all pairs of rectangles
        for i in range(num_nodes):
            for j in range(i + 1, num_nodes):
                force_x, force_y = calculate_repulsive_force(
                    nodes[i], nodes[j], separation, force_strength
                )

                forces[i, 0] += force_x
                forces[i, 1] += force_y
                forces[j, 0] -= force_x
                forces[j, 1] -= force_y

        # Update positions based on forces
        for i in range(num_nodes):
            if fixed_index is not None and i == fixed_index:
                continue  # Skip the fixed node

            # Update velocity with damping
            velocities[i, 0] = velocities[i, 0] * damping + forces[i, 0]
            velocities[i, 1] = velocities[i, 1] * damping + forces[i, 1]

            # Limit step size to prevent explosive movement (optimized)
            velocity_magnitude_squared = velocities[i, 0] ** 2 + velocities[i, 1] ** 2
            if velocity_magnitude_squared > step_size_squared:
                velocity_magnitude = math.sqrt(velocity_magnitude_squared)
                scale_factor = step_size / velocity_magnitude
                velocities[i, 0] *= scale_factor
                velocities[i, 1] *= scale_factor

            # Update position
            old_x, old_y = nodes[i]["x"], nodes[i]["y"]
            nodes[i]["x"] += velocities[i, 0]
            nodes[i]["y"] += velocities[i, 1]

            # Calculate movement for convergence check (optimized)
            dx = nodes[i]["x"] - old_x
            dy = nodes[i]["y"] - old_y
            movement = math.sqrt(dx * dx + dy * dy)
            max_movement = max(max_movement, movement)

        # Check for convergence
        if max_movement < convergence_threshold:
            break

    return nodes
