import requests

from models.workflowGraph import WorkflowGraph


def is_authorized(workflow: WorkflowGraph):
    url = "https://app-dev.rapidinnovation.dev/api/v1/pipedream/apps/"
    nodes = workflow.nodes
    for node_id, node in nodes.items():
        try:
            if node.data.type == "mcp":
                name_slug = node.data.definition["name_slug"]
                response = requests.get(url + name_slug)
                connected_users = response.json()["connected_users"]
                if len(connected_users) == 0:
                    return False
        except:
            continue
    return True


def save_workflow(workflow: WorkflowGraph, workflow_id: str, httpbearer: str):
    workflow = workflow.compile()
    url = f"https://app-dev.rapidinnovation.dev/api/v1/workflows/{workflow_id}"
    headers = {
        "Authorization": f"Bearer {httpbearer}",
        "Content-Type": "application/json",
    }
    response = requests.patch(url, json={"workflow_data": workflow}, headers=headers)
    return response.status_code == 200


def get_workflow(workflow_id: str, httpbearer: str):
    url = f"https://app-dev.rapidinnovation.dev/api/v1/workflows/{workflow_id}"
    headers = {
        "Authorization": f"Bearer {httpbearer}",
        "Content-Type": "application/json",
    }
    response = requests.get(url, headers=headers)
    workflow = response.json()["workflow"]
    workflow = WorkflowGraph(
        nodes={node["id"]: node for node in workflow["nodes"]},
        edges={edge["id"]: edge for edge in workflow["edges"]},
    )
    return workflow


def execute_workflow(workflow_id: str, httpbearer: str, input_data: dict):
    url = f"https://ruh-test-api.rapidinnovation.dev/api/v1/workflow-execute/execute-ruh-orchestration/{workflow_id}"
    headers = {
        "Authorization": f"Bearer {httpbearer}",
        "Content-Type": "application/json",
    }
    response = requests.post(url, json=input_data, headers=headers)
    if response.status_code != 200:
        raise [
            {
                "error": response.content,
                "status_code": response.status_code,
            }
        ]
    else:
        correlation_id = response.json()["correlation_id"]
        output = []
        url = f"https://ruh-test-api.rapidinnovation.dev/api/v1/workflow-execute/stream/{correlation_id}"
        for event in requests.get(url, stream=True):
            output.append(event)
        output = [o for o in output if o.event == "output"]
        return output
