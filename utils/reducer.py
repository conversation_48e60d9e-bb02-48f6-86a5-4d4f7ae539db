from typing import Any


def todo_reducer(left: list[dict[str, Any]], right: list[dict[str, Any]]) -> list[dict[str, Any]]:
    for todo in right:
        if "operation" not in todo:
            left.append(todo)
            continue
        ops = todo.pop("operation")
        if ops == "add":
            left.append(todo)
        elif ops == "delete":
            task_key = todo.pop("task_key")
            for i, l_todo in enumerate(left):
                if l_todo["task_key"] == task_key:
                    left.pop(i)
                    break
    return left
