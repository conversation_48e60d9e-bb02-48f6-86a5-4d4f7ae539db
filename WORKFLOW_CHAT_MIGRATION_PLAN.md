# Workflow Chat Migration Plan: Strands to LangGraph

## Overview

This document outlines the comprehensive migration plan for transforming the `app/workflow_chat/` system from the Strands framework to LangGraph, with a focus on implementing tool-based workflow creation instead of direct JSON manipulation.

## Current Architecture Analysis

### Strands-Based System
- **Main Agent**: Orchestrates workflow creation process
- **Planner Agent**: Generates workflow plans and validates tool availability  
- **Workflow Generation Agent**: Creates final executable workflow JSON
- **Tools**: RAG search, context retrieval, pre/post processing
- **Session Management**: MongoDB-based session storage
- **Streaming**: Custom SSE events for real-time updates

### Key Issues with Current System
- Direct JSON manipulation prone to errors
- Scattered state management across agents
- Custom streaming implementation
- Limited validation during workflow creation
- Tight coupling between agents

## Target LangGraph Architecture

### Core Components
- **StateGraph**: Centralized state management with TypedDict
- **Node Functions**: Convert agents to LangGraph nodes
- **Tool-Based Builders**: Structured workflow construction
- **Native Streaming**: LangGraph's astream_events
- **MongoDB Checkpointing**: Built-in session persistence

### Benefits
- ✅ Type-safe state management
- ✅ Structured workflow validation
- ✅ Better error handling and recovery
- ✅ Native streaming capabilities
- ✅ Improved debugging and observability
- ✅ Scalable execution engine

## State Management Design

### WorkflowChatState Schema
```python
class WorkflowChatState(MessagesState):
    # Core workflow data
    current_workflow: Dict[str, Any]
    workflow_generated: bool
    final_workflow: Optional[Dict[str, Any]]
    
    # Session and user context
    session_id: str
    user_prompt: str
    
    # Agent coordination
    current_step: str  # "planning", "generation", "validation", "complete"
    plan: Optional[str]
    feedback: Optional[str]
    
    # Tool validation and context
    available_tools: List[Dict[str, Any]]
    rag_results: List[Dict[str, Any]]
    
    # Error handling
    errors: List[str]
    warnings: List[str]
```

## Tool-Based Workflow Builder

### New Approach
Instead of generating JSON directly, implement structured tools:

#### Core Tools
1. **`add_node`**: Add validated nodes to workflow
2. **`add_edge`**: Create connections between nodes
3. **`delete_node`**: Remove nodes and associated edges
4. **`delete_edge`**: Remove specific connections
5. **`update_node`**: Modify existing node properties
6. **`get_node_info`**: Retrieve detailed node information
7. **`rag_search`**: Search for available components and tools
8. **`context_tool`**: Get contextual information for workflow building

#### Benefits
- **Comprehensive CRUD Operations**: Full create, read, update, delete functionality for nodes and edges
- **Built-in Validation**: Prevents invalid workflows with real-time structure checking
- **Contextual Intelligence**: RAG search and context tools provide intelligent recommendations
- **Type Safety**: Ensures correct node/edge structure with validation
- **Better Error Handling**: Detailed error messages and success confirmations
- **Extensibility**: Easy to add new node types and validation rules

## Node Migration Strategy

### Agent → Node Conversion

| Current Agent | New Node Function | Responsibility |
|---------------|-------------------|----------------|
| Main Agent | `coordinator_node` | Route between steps, manage state |
| Planner Agent | `planner_node` | Generate plans, validate tools |
| Workflow Generation Agent | `generator_node` | Build workflow using tools |
| Post Processing | `post_processor_node` | Layout and final validation |

### Node Implementation Pattern
```python
async def node_function(state: WorkflowChatState) -> Dict[str, Any]:
    # Process current state
    # Call tools if needed
    # Return state updates
    return {"key": "updated_value"}
```

## Implementation Roadmap

### Phase 1: Foundation
- [ ] Create state schema (`state.py`)
- [ ] Implement MongoDB checkpointing (`checkpointing.py`)
- [ ] Build core workflow tools (`tools/workflow_builder.py`)
- [ ] Set up project structure and dependencies

### Phase 2: Core Migration
- [ ] Migrate main agent → coordinator node
- [ ] Migrate planner agent → planner node (reuse existing logic)
- [ ] Migrate workflow generation → generator node
- [ ] Implement post-processing node (reuse existing logic)

### Phase 3: Tools and Integration
- [ ] **Minor tweaks** to RAG search for LangGraph compatibility
- [ ] **Minor tweaks** to context retrieval tools
- [ ] Create main StateGraph (`graph.py`)
- [ ] Implement event streaming (`streaming.py`)
- [ ] Build entry point (`main.py`)

### Phase 4: Testing and Integration
- [ ] Unit tests for each component
- [ ] Integration tests for full workflow
- [ ] Performance benchmarking vs current system

### Phase 5: Deployment and Validation
- [ ] Feature flag implementation for gradual rollout
- [ ] Production deployment and monitoring setup
- [ ] User acceptance testing and feedback collection

## File Structure

```
app/workflow_chat_v2/
├── __init__.py
├── state.py                    # State definitions
├── graph.py                    # Main LangGraph definition
├── checkpointing.py           # MongoDB integration
├── streaming.py               # Event streaming
├── main.py                    # Entry point
├── nodes/
│   ├── __init__.py
│   ├── coordinator.py         # Main coordinator node
│   ├── planner.py            # Planning logic
│   ├── generator.py          # Workflow generation
│   └── post_processor.py     # Post-processing
├── tools/
│   ├── __init__.py
│   ├── workflow_builder.py   # Tool-based builders
│   ├── rag.py               # RAG search (updated)
│   ├── context.py           # Context tools (updated)
│   └── validation.py        # Workflow validation
└── tests/
    ├── test_nodes.py
    ├── test_tools.py
    ├── test_graph.py
    └── test_integration.py
```

## Key Implementation Notes

### Existing Code Reuse (Significant Efficiency Gains)
- **RAG Search**: Current `app/workflow_chat/tools/RAG.py` requires only minor interface tweaks vs complete rewrite
- **Context Tools**: Existing context retrieval logic preserved with updated return formats vs complete rewrite
- **Post-processing**: Current layout and validation logic can be directly reused vs complete rewrite
- **Session Management**: MongoDB session handling integrates seamlessly with LangGraph checkpointing
- **Agent Logic**: Core planning and generation logic can be adapted rather than rewritten

### Implementation Efficiency
- **Approach**: Strategic code reuse maximizes existing investments
- **Risk Reduction**: Preserving proven logic reduces bugs and testing overhead
- **Faster Delivery**: Leveraging existing components accelerates development

### New Tool-Based Workflow Builder
The new system will implement 9 core tools for structured workflow creation:

1. **Node Management**: `add_node`, `delete_node`, `update_node`, `get_node_info`
2. **Edge Management**: `add_edge`, `delete_edge`
3. **Intelligence**: `rag_search` (adapted from existing), `context_tool` (adapted from existing)
4. **Validation**: `validate_workflow_structure`

### Code Reuse Strategy
- **RAG Search Tool**: Adapt existing `RAG_search` function with minor interface changes for LangGraph compatibility
- **Context Tool**: Reuse existing context retrieval logic with updated return format
- **Validation Logic**: Leverage current workflow validation rules in the new validation tool











## Migration Considerations

### Backward Compatibility
- Maintain existing API endpoints during transition
- Support both Strands and LangGraph implementations in parallel
- Gradual migration of sessions from old to new system
- Feature flags to control rollout

### Performance Considerations
- **Memory Usage**: LangGraph state management vs Strands agent state
- **Execution Speed**: Compare graph execution performance
- **Streaming Latency**: Ensure real-time updates remain responsive
- **Database Load**: Monitor MongoDB checkpoint performance

### Error Handling and Recovery
- **State Recovery**: LangGraph checkpointing for crash recovery
- **Validation Errors**: Better error messages with tool-based validation
- **Rollback Strategy**: Ability to fallback to Strands implementation
- **Monitoring**: Enhanced observability with LangGraph tracing

### Testing Strategy
- **Unit Tests**: Individual node and tool testing
- **Integration Tests**: Full workflow generation scenarios
- **Performance Tests**: Load testing and benchmarking
- **Compatibility Tests**: Ensure existing workflows still work

## Success Metrics

### Technical Metrics
- [ ] **Reduced Errors**: 50% reduction in workflow generation errors
- [ ] **Improved Performance**: Maintain or improve response times
- [ ] **Better Validation**: 90% of invalid workflows caught before generation
- [ ] **Enhanced Debugging**: Detailed execution traces available

### User Experience Metrics
- [ ] **Workflow Quality**: Higher success rate for complex workflows
- [ ] **Error Messages**: More actionable error feedback
- [ ] **Response Time**: Maintain sub-3-second initial response
- [ ] **System Reliability**: 99.9% uptime during migration

## Risk Mitigation

### High-Risk Areas
1. **State Migration**: Complex state transformation between systems
2. **Session Continuity**: Maintaining user sessions during transition
3. **Tool Compatibility**: Ensuring all existing tools work with LangGraph
4. **Performance Regression**: Potential slowdown during initial deployment

### Mitigation Strategies
1. **Phased Rollout**: Gradual migration with rollback capability
2. **Comprehensive Testing**: Extensive testing before production deployment
3. **Monitoring**: Real-time monitoring of key metrics
4. **Documentation**: Detailed migration guides and troubleshooting

## Conclusion

This migration plan provides a comprehensive roadmap for transforming the workflow chat system from Strands to LangGraph. The key benefits include:

- **Improved Reliability**: Better error handling and state management
- **Enhanced Validation**: Tool-based workflow construction with built-in validation
- **Better Observability**: Native LangGraph tracing and debugging
- **Future-Proof Architecture**: Built on modern, actively maintained framework

The **phased implementation approach** leverages existing code reuse, allowing for careful migration while maintaining system stability and user experience. The approach is optimized based on the fact that RAG and context tools require only minor tweaks rather than complete rewrites.

## Next Steps

1. **Review and Approval**: Stakeholder review of migration plan
2. **Resource Allocation**: Assign development team and timeline
3. **Environment Setup**: Prepare development and testing environments
4. **Phase 1 Implementation**: Begin with foundation components
5. **Continuous Monitoring**: Track progress and adjust plan as needed

---

*This document should be reviewed and updated as the migration progresses.*
