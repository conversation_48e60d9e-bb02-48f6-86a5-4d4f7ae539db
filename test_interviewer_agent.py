#!/usr/bin/env python3
"""
Test script for the Interviewer Agent.
This script demonstrates how the interviewer agent gathers requirements from users.
"""

import os
import json
from models.state import State
from models.workflowGraph import WorkflowGraph
from graph.interviewer_agent_graph import create_interviewer_graph

def test_interviewer_agent():
    """Test the interviewer agent with a sample user prompt."""
    
    # Set up environment
    if not os.getenv("OPENAI_API_KEY"):
        print("Please set OPENAI_API_KEY environment variable")
        return
    
    # Create initial state
    initial_state = State(
        workflow_graph=WorkflowGraph(),
        user_prompt="I want to create a workflow that processes customer data from a CSV file, validates the email addresses, and sends personalized welcome emails to new customers."
    )
    
    print("=== INTERVIEWER AGENT TEST ===")
    print(f"Initial user prompt: {initial_state.user_prompt}")
    print("\n" + "="*50)
    
    # Create interviewer graph
    interviewer_graph = create_interviewer_graph()
    
    # Run the interviewer agent
    try:
        print("\nStarting interviewer agent...")
        result = interviewer_graph.invoke(initial_state)
        
        print("\n=== INTERVIEW RESULTS ===")
        print(f"Interview complete: {result.interview_complete}")
        print(f"Requirements gathered: {bool(result.workflow_requirements)}")
        
        if result.workflow_requirements:
            print("\nWorkflow Requirements:")
            print(json.dumps(result.workflow_requirements, indent=2))
        
        if result.missing_information:
            print(f"\nMissing information: {result.missing_information}")
        
        if result.clarification_questions:
            print(f"\nQuestions asked: {len(result.clarification_questions)}")
            for i, question in enumerate(result.clarification_questions, 1):
                print(f"  {i}. {question}")
        
        # Show the final messages
        if result.interviewer_messages:
            print(f"\nFinal agent message:")
            last_message = result.interviewer_messages[-1]
            if hasattr(last_message, 'content'):
                print(last_message.content)
        
    except Exception as e:
        print(f"Error running interviewer agent: {str(e)}")
        import traceback
        traceback.print_exc()

def interactive_test():
    """Interactive test where user can provide their own prompt."""
    
    if not os.getenv("OPENAI_API_KEY"):
        print("Please set OPENAI_API_KEY environment variable")
        return
    
    print("=== INTERACTIVE INTERVIEWER AGENT TEST ===")
    user_prompt = input("Enter your workflow request: ").strip()
    
    if not user_prompt:
        print("No prompt provided, using default example.")
        user_prompt = "I want to create a workflow that processes customer data from a CSV file, validates the email addresses, and sends personalized welcome emails to new customers."
    
    # Create initial state
    initial_state = State(
        workflow_graph=WorkflowGraph(),
        user_prompt=user_prompt
    )
    
    print(f"\nProcessing prompt: {user_prompt}")
    print("\n" + "="*50)
    
    # Create and run interviewer graph
    interviewer_graph = create_interviewer_graph()
    
    try:
        result = interviewer_graph.invoke(initial_state)
        
        print("\n=== INTERVIEW RESULTS ===")
        print(f"Interview complete: {result.interview_complete}")
        
        if result.interviewer_messages:
            last_message = result.interviewer_messages[-1]
            if hasattr(last_message, 'content'):
                print(f"\nAgent response:\n{last_message.content}")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_test()
    else:
        test_interviewer_agent()
